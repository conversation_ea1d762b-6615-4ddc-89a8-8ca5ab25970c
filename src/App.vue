<template>
  <div id="app">
    <router-view />
    <yxim v-if="hasLoginToken" :showim="showim" @hideIM="hideIM"></yxim>
  </div>
</template>
<script>
import linkConfig from '../config/link.js';
import yxim from '@/components/IM/index.vue';
import isLogin from './utils/isLogin.js';
export default {
  name: 'App',
  components: { yxim },
  data() {
    return {};
  },
  computed: {
    imunreadcount() {
      return this.$store.getters.imunreadcount;
    },
    showim() {
      return this.$store.getters.imstate;
    },
    hasLoginToken() {
      return !!localStorage.getItem('token') || !!this.$store.getters.token;
    },
  },
  created() {
    console.log('aaaa', `?productSn=${location.pathname.split('/')[2]}`);

    if (location.host == 'kkzhw.com') {
      // if(location.host=='127.0.0.1:9528'){
      const searchParams = location.search; // 获取当前的查询参数
      location.href = 'https://www.kkzhw.com' + searchParams; // 带上参数重定向
      return;
    }
    if (location.host == 'yokoye.com') {
      // if(location.host=='127.0.0.1:9528'){
      const searchParams = location.search; // 获取当前的查询参数
      location.href = 'https://www.yokoye.com' + searchParams; // 带上参数重定向
      return;
    }
    // 跳 https
    if (!location.port && window.location.protocol !== 'https:') {
      var newUrl =
        'https://' +
        window.location.hostname +
        window.location.pathname +
        window.location.search +
        window.location.hash;
      location.href = newUrl;
      return;
    }
    this.requireNotification();
    // var arrUrl = str.split('#/');
    // var str = window.location.href;
    // if (str.includes('#/')) {
    //   var arrUrl = str.split('#/');
    //   window.location.href = arrUrl[0] + arrUrl[1];
    //   console.log(arrUrl[0] + arrUrl[1]);
    // }
    // if (
    //   /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    //     navigator.userAgent
    //   )
    // ) {
    //   const currentUrl = location.href;
    //   const hasYokoye = /yokoye/.test(currentUrl);
    //   const { host, pathname, search } = location;
    //   const sanitizedHost = host.replace(/^www\./, '');
    //   const rules = [
    //       {
    //           regex: /\/playList/,
    //           getRedirectUrl: () => {
    //               const base = hasYokoye ? `https://m.${sanitizedHost}` : linkConfig.linkH5url;
    //               return `${base}/pages/accountList/accountList${search}`;
    //           }
    //       },
    //       {
    //           regex: /\/playDetail/,
    //           getRedirectUrl: () => {
    //               const base = hasYokoye ? `https://m.${sanitizedHost}` : linkConfig.linkH5url;
    //               return `${base}/pages/accountDetail/accountDetail${search}`;
    //           }
    //       },
    //       {
    //           regex: /\/gd/,
    //           getRedirectUrl: () => {
    //               const base = hasYokoye ? `https://m.${sanitizedHost}` : linkConfig.linkH5url;
    //               const newSearch = `?productSn=${pathname.split('/')[2]}`;
    //               return `${base}/pages/accountDetail/accountDetail${newSearch}`;
    //           }
    //       }
    //   ];
    //   for (const rule of rules) {
    //       if (rule.regex.test(currentUrl)) {
    //           window.location.href = rule.getRedirectUrl();
    //           return;
    //       }
    //   }
    //   if (hasYokoye) {
    //       window.location.href = `https://m.${sanitizedHost}`;
    //       return;
    //   }
    //   window.location.href = linkConfig.linkH5url;
    //   // if (location.href.indexOf('/playList') > 0&&location.href.indexOf('yokoye') > 0) {
    //   //   window.location.href = `m.${location.host}/pages/accountList/accountList${location.search}`;
    //   //   return;
    //   // }
    //   // if (location.href.indexOf('/playDetail') > 0&&location.href.indexOf('yokoye') > 0) {
    //   //   window.location.href = `m.${location.host}/pages/accountDetail/accountDetail${location.search}`;
    //   //   return;
    //   // }
    //   // if (location.href.indexOf('/gd') > 0&&location.href.indexOf('yokoye') > 0) {
    //   //   // 兼容商品详情用编码路径的规则
    //   //   const search = `?productSn=${location.pathname.split('/')[2]}`;
    //   //   window.location.href = `m.${location.host}/pages/accountDetail/accountDetail${search}`;
    //   //   return;
    //   // }
    //   // window.location.href = linkConfig.linkH5url;
    //   // return;
    // }
  },
  mounted() {
    if (isLogin()) {
      localStorage.setItem('isPeacock', true);
      this.$store.dispatch('getUserInfoStore');
    }
  },
  methods: {
    requireNotification() {
      if ('Notification' in window) {
        // 检查用户是否已授权
        if (Notification.permission === 'granted') {
          // 通知权限已授予，可以安全地显示通知
        } else if (Notification.permission !== 'denied') {
          // 权限尚未确定，请求权限
          Notification.requestPermission().then(function (permission) {});
        }
      } else {
        // 浏览器不支持 Notification API
        console.log('Your browser does not support desktop notifications.');
        // 可以选择在这里显示一条页面内的消息或执行其他回退逻辑
      }
    },
    hideIM() {
      const { store } = window.__xkit_store__;
      store.uiStore.selectSession('');
      this.$store.dispatch('ToggleIM', false);
    },
  },
};
</script>
<style type="text/css" lang="scss">
.ant-dropdown-menu-item:nth-child(2) {
  display: none !important;
}
.kk_custom_msg_content {
  .msg-flexstart {
    align-items: flex-start;
  }
  .msg-productImg {
    width: 85.7px;
    height: 60px;
    border-radius: 10.283px;
    margin-right: 10.283px;
  }
  .msg-red {
    color: #ff720c;
  }
}

.leftNav_container {
  width: 285px;

  background: linear-gradient(180deg, #ffe4c9 -21.57%, #fff -7.76%);
  border-radius: 24px;
  overflow: hidden;
  padding: 20px 16px;
  .main_tit {
    background: #fe5a1e;
  }
  .main_tit_one {
    background: blue;
  }
  .el-menu-item {
    padding: 0px !important;
    text-indent: 54.848px;
    color: #2d2d2d;
    /* 小字标题 */
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    height: 46.278px;
    display: flex;
    align-items: center;
    box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
    // border-radius: 24px;
  }
  .el-menu-item.is-active,
  .el-menu-item:focus,
  .el-menu-item:hover {
    color: #1b1b1b !important;
    background: #fbf9f7;
    // border-left-color: #ff6716;
  }
  /*.el-submenu.is-active .el-submenu__title,*/
  .el-submenu__title {
    color: #1b1b1b;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    padding: 0px;
    padding-left: 17.14px;
    height: 75.416px;
    display: flex;
    align-items: center;
    border-radius: 20.567px;
  }
  .el-menu-item-group__title {
    padding: 0px;
  }
  .el-submenu__title:hover {
    background: #fbf9f7;
  }
}
.chat-message-input-wrap .chat-message-input-content {
  min-height: 90px;
  background: #f2f2f2;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  border-radius: 24px !important;
}
/* 主色ff6716。重要字色222222   辅助字色909090  背景灰底 f4 f4 f4 */
/* 文章场景相关 */
$blue: #1875e7;
// $btnGraniend: radial-gradient(
//   ellipse at top center,
//   #ff7a00 -48%,
//   rgba(255, 137, 137, 1) -89%,
//   #ff7a00 36%,
//   #ffc700 100%
// );
$btnGraniend: url(../static/imgs/btn_bk1x.png);

.btnGranient {
  background: $btnGraniend;
  background-size: cover;
}
html,
body {
  font-weight: 500;
  font-family: -apple-system,BlinkMacSystemFont,PingFang SC\",Lantinghei SC,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;
  overflow-x: hidden;
  // line-height: 1.15;
  line-height: normal;
  background: linear-gradient(180deg, #fdf5ed 0, #fff 200.46%);
}
a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}
body::-webkit-scrollbar {
  width: 6px;
}
body::-webkit-scrollbar-thumb {
  background: #999;
  border-radius: 5px;
}

.scrollPageSmoth {
  width: 100%;
  height: 100vh;
  overflow-y: scroll;
}
.scrollPageSmoth::-webkit-scrollbar {
  width: 6px;
}
.scrollPageSmoth::-webkit-scrollbar-thumb {
  background: #999;
  border-radius: 5px;
}

.pdTopBottom {
  padding: 34.28px 0;
}
.mgBottomSmall {
  margin-bottom: 6px;
}
.cursor {
  cursor: pointer;
}
.safe_width {
  width: 1200px;
  margin: 0 auto;
}
.dark_container {
  width: 100%;
  // background: #f4f4f4;
  background: linear-gradient(180deg, #fdf5ed 0%, #fff 200%);
}
.white_container {
  width: 100%;
  background: #fff;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
}
.spaceEnd {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.spaceAlignCenter {
  display: flex;
  align-items: center;
}
.spaceBetween {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.spaceBetweenNoAi {
  display: flex;
  justify-content: space-between;
}
.spaceStart {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.spaceStartNotAi {
  display: flex;
  justify-content: flex-start;
}
.spaceAround {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.spaceCenter {
  display: flex;
  justify-content: center;
  align-items: center;
}
.alignItemsStart {
  align-items: flex-start;
}
.gamePlay_wrap {
  font-size: 20px;
  font-weight: 500;
  color: #909090;
}
.gamePlay_wrap_pic {
  width: 80px;
  height: 80px;
  border-radius: 10px;
  margin-right: 20px;
  overflow: hidden;
}
.el-breadcrumb {
  font-size: 16px;
  line-height: 30px;
  font-weight: 400;
  letter-spacing: 0.64px;
}
.el-breadcrumb__item {
  .el-breadcrumb__separator {
    color: #848484 !important;
    &::before {
      content: '>';
      font-weight: bold;
    }
  }
}
.el-breadcrumb__inner_text {
  span {
    color: #848484;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    letter-spacing: 0.64px;
  }
}
.el-breadcrumb__inner {
  color: #f58e27 !important;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400 !important;
  letter-spacing: 0.64px;
}
.el-breadcrumb__inner.is-link {
  font-weight: 400 !important;
  color: #848484 !important;
}

.el-breadcrumb__inner.is-link:hover {
  color: #f58e27 !important;
  cursor: pointer;
}
.page_comStyle {
  background: #fff;
  box-sizing: border-box;
  padding: 20px;
  border-radius: 6px;
}
.borderBottom {
  border-bottom: 1px solid #eaeaea;
}
.borderTop {
  border-top: 1px solid #eaeaea;
}
.icon_typeS {
  width: 16px;
  margin-right: 2px;
}
.mainTit {
  font-size: 16px;
  font-weight: 500;
  color: #222222;
}

.textOneLine {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.text_linTwo {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2 !important;
  line-clamp: 2 !important;
  -webkit-box-orient: vertical;
}
.text_linThree {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}
.colorPrimay {
  color: #ff6716;
}
.colorPrimay:hover {
  color: #ff6716;
}
.logo_pic {
  width: 400px;
  height: 90px;
  cursor: pointer;
}
.empty_box {
  text-align: center;
  font-size: 14px;
  color: #999;
  padding-top: 60px;
  padding-bottom: 60px;
}
.pageDate {
  line-height: 26px;
  font-size: 14px;
}
.pageDate img {
  display: block;
  margin: 0 auto;
}
/********** table *************/
.table_main {
  width: 100%;
  border: 1px solid #ebeef5;
  border-bottom: none;
  border-right: none;
  color: #666;
  font-weight: 500;
  margin: 20px 0;
}

.table_main th {
  background: #f5f5f5;
  padding: 14px 8px;
}

.table_main td {
  font-size: 14px;
  padding: 14px 8px;
  text-align: left;
  border-bottom: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
}

.table_main td.fix_width {
  width: 90px;
}

.header_fixed {
  padding: 15px 0 15px;
  padding-left: 120px;
}

.picUpload_wrap {
  width: 180px;
  height: 180px;
  border-radius: 12px;
  border: 0;
  position: relative;
  text-align: center;
  line-height: 180px;
  font-size: 28px;
  color: #8c939d;
  overflow: hidden;
}
.picUpload_btn {
  position: absolute;
  z-index: 4;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  opacity: 0;
  cursor: pointer;
}
.picUpload_pic {
  position: absolute;
  z-index: 1;
  left: 0px;
  right: 0px;
  top: 0px;
  bottom: 0px;
  width: 100%;
  height: 100%;
}
.blockAll {
  display: block;
  width: 100%;
  height: 52.114px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.line .el-form-item .el-form-item__content {
  text-align: left;
}

.fontFamilg {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB',
    'Microsoft Yahei', sans-serif;
}

.el-input.is-active .el-input__inner,
.el-input__inner:focus,
.el-select .el-input.is-focus .el-input__inner {
  border-color: #ff720c;
  outline: 0;
}
.el-pagination.is-background .el-pager li:not(.disabled):hover {
  color: #ff6716;
}
.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: #ff6716;
  color: #fff;
}
.el-checkbox__input.is-checked + .el-checkbox__label {
  color: #ff6716;
}
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #ff6716;
  border-color: #ff6716;
}
.el-dialog__headerbtn:focus .el-dialog__close,
.el-dialog__headerbtn:hover .el-dialog__close {
  color: #ff6716;
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
  color: #ff6716;
  background-color: #fff;
}
.el-collapse-item__header {
  border-bottom: none;
}
.el-collapse {
  border-bottom: none;
  border-top: none;
}
.el-select-dropdown__item {
  font-family: 'PingFang SC';
  font-weight: 400;
  line-height: normal;
}
.el-select-dropdown__item.selected {
  color: #ff720c;
  font-weight: 600;
}
.el-select .el-input__inner:focus {
  border-color: #ff6716;
}
.playSearch_wrap .el-collapse-item__arrow {
  margin-top: 15px;
  font-size: 20px;
}
.el-radio__input.is-checked .el-radio__inner {
  border-color: #ff6716;
  background: #ff6716;
}
.el-radio__input.is-checked + .el-radio__label {
  color: #ff6716;
}
.el-textarea__inner:focus {
  outline: 0;
  border-color: #ff6716;
}
.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:not(.is-disabled):hover {
  background-color: #fdf5ed;
  font-family: 'PingFang SC';
  // color: #ff720c;
  color: #969696;
}
.nav_item .el-dropdown-menu {
  width: 130px;
  text-align: center;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type='number'] {
  -moz-appearance: textfield;
  line-height: 1;
}
.el-dropdown-link {
  display: block;
  height: 100%;
}
.el-dropdown-link_title {
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.64px;
}
.nav_container .el-popper {
  overflow: auto;
  margin-top: -10px;
}
.nav_container .el-dropdown-menu {
  top: 50px !important;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  margin: 0;
}
.nav_container .el-dropdown-menu__item {
  font-size: 16px;
  line-height: 40px;
  color: #969696 !important;
}
/*列表 滑动文字展示*/
.el-tooltip__popper {
  width: 800px;
  background: rgba(0, 0, 0, 0.8);
  padding-bottom: 15px;
}
.el-tooltip__popper.is-dark {
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
}

.el-button--default:focus,
.el-button--default:hover {
  color: #ff6917;
  border-color: #ff6917;
  background-color: #fff4ef;
}
// .el-button--primary,
// .el-button--primary:focus,
// .el-button--primary:hover {
//   // background: #ff6917;
//   // border: 1px solid #ff9600;
//   // color: #fff;
//   // background: var(
//   //   --Main-color,
//   //   radial-gradient(
//   //     238.39% 44.19% at 96.59% 31.25%,
//   //     rgba(255, 255, 255, 0.1) 0%,
//   //     rgba(255, 255, 255, 0) 100%
//   //   ),
//   //   radial-gradient(
//   //     182.56% 55.34% at 5.68% 100%,
//   //     rgba(246, 251, 34, 0.31) 0%,
//   //     rgba(255, 158, 69, 0) 100%
//   //   ),
//   //   radial-gradient(
//   //     137.51% 118.3% at 32.95% 0%,
//   //     rgba(255, 137, 137, 0.83) 21.25%,
//   //     rgba(255, 169, 106, 0.51) 88.62%
//   //   ),
//   //   radial-gradient(
//   //     178.09% 220.16% at 94.89% -132.81%,
//   //     #ff7a00 67.59%,
//   //     rgba(255, 199, 0, 0.38) 100%
//   //   )
//   // );
//   background: $btnGraniend;
//   background-size: cover;
//   color: #fff;
// }
.el-message-box__headerbtn:hover .el-message-box__close {
  color: #ff6917;
}

// .el-loading-spinner i,
// .el-loading-spinner > .el-loading-text {
//   // color: #ff6917;

//   font-size: 26px;
//   font-family: YouSheBiaoTiHei;
//   font-weight: 400;
//   margin-left: 11px;

//   color: transparent !important;
//   background: radial-gradient(
//       31.25% 236.33% at 96.59% 31.25%,
//       rgba(255, 255, 255, 0.1) 0%,
//       rgba(255, 255, 255, 0) 10%
//     ),
//     radial-gradient(
//       39.2% 181% at 5.68% 100%,
//       rgba(246, 251, 34, 0.306) 0%,
//       rgba(255, 158, 69, 0) 60%
//     ),
//     radial-gradient(
//       71.74% 117.31% at 32.95% 0%,
//       rgba(255, 137, 137, 0.828) 21.25%,
//       rgba(255, 169, 106, 0.513) 90%
//     ),
//     radial-gradient(
//       92.05% 200% at 94.89% -132.81%,
//       rgba(255, 122, 0, 1) 67.59%,
//       rgba(255, 199, 0, 0.38) 100%
//     ),
//     linear-gradient(0deg, rgba(255, 245, 0, 1), rgba(255, 245, 0, 1)) !important;
//   -webkit-background-clip: text !important;
//   background-clip: text !important;
// }
// .el-loading-spinner i {
//   font-size: 28px;
// }
// .el-loading-spinner {
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   background-image: url('../static/imgs/logo_icon.svg');
//   background-repeat: no-repeat;
//   background-size: 47px 50px;
//   width: 200px;
//   height: 50px;
//   top: 40%;
//   left: 50%;
// }
.el-loading-spinner i,
.el-loading-spinner .el-loading-text {
  color: #ff6917;
}
.el-loading-spinner i {
  font-size: 28px;
}
.el-loading-mask {
  position: fixed;
}

.el-loading-spinner .circular {
  /*隐藏 之前  element-ui  默认的 loading 动画*/

  display: none;
}

.fixeIndex {
  z-index: 3008 !important;
}
.image-slot {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  background: #f3f3f3;
  justify-content: center;
}
.topTips_tit {
  font-size: 16px;
  color: #ff6700;
  font-weight: 500;
  padding: 6px 0 10px;
}
.kf_icon {
  z-index: 88;
  background: #fff;
  cursor: pointer;
  border: 1px solid #f5a332;
  position: fixed;
  right: 23px;
  top: 50%;
  margin-top: 200px;
  width: 100px;
  height: 100px;
  text-align: center;
  border-radius: 50%;
  display: flex;
  line-height: 72px;
  align-items: center;
  text-align: center;
  justify-content: center;
}
.mask_container {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1399;
  background: rgba(0, 0, 0, 0.65);
  backdrop-filter: blur(10px);
}
.msk_body {
  width: 683px;
  height: 583px;
  flex-shrink: 0;
  // background: url(../static/s_body.jpg) no-repeat center top;
  // background-size: 100% 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 24px;
  background: #fffcf9;
}
.imcount {
  position: absolute;
  top: -7px;
  right: 20px;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  background-color: #fe5a1e;
  border-radius: 50%;
  font-size: 14px;
  color: #fff;
}
.imNotify {
  cursor: pointer;
  .el-icon-info {
    width: 30px;
    height: 30px;
    background: url(../static/four.png) no-repeat;
    background-size: contain;
  }
  .el-icon-info::before {
    content: '';
  }
  .el-notification__content {
    width: 230px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
    text-align: left;
  }
}

.topTips_con {
  line-height: 22px;
  font-size: 14px;
  font-family: -apple-system,BlinkMacSystemFont,PingFang SC\",Lantinghei SC,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;
  font-weight: 600;
}
.note-waring {
  // color: #fc6116;
  color: #ffb74a;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.56px;
}
@keyframes shake {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  50% {
    transform: translateX(5px);
  }
  75% {
    transform: translateX(-3px);
  }
  100% {
    transform: translateX(0);
  }
}

.shake-animation {
  animation: shake 0.2s; /* 持续时间0.5秒 */
  animation-iteration-count: 3; /* 无限次重复 */
}
/* 已移除隐藏已读状态的样式，让已读/未读状态正常显示 */
.el-cascader .el-input .el-input__inner:focus,
.el-cascader .el-input.is-focus .el-input__inner {
  border-color: #ff6716;
}
.el-cascader-node.in-active-path,
.el-cascader-node.is-active,
.el-cascader-node.is-selectable.in-checked-path {
  color: #ff6716;
}
.el-cascader-panel .el-cascader-node .el-checkbox {
  position: absolute;
  z-index: 1;
  left: 10px;
  right: 10px;
  opacity: 0;
}
.el-cascader-panel li[aria-haspopup='true'] .el-checkbox {
  display: none;
}
.el-form-item__error {
  color: #ff720c;
}

.footerCt {
  width: 100%;
  // height: 314px;
  background: #fffcf8;
}

.listContentBk {
  background: linear-gradient(180deg, #fdf5ed 36.61%, #fff 53.46%, #ffe1c3 100%)
    local !important;
}
.el-menu-vertical-demo .el-submenu__icon-arrow {
  color: #000;
  font-weight: bold;
  font-size: 16px;
  margin-top: -8px;
}

.my-bread .el-breadcrumb__item .el-breadcrumb__inner {
  color: #f58e27 !important;
}

.my-bread .el-breadcrumb__item .is-link {
  color: #848484 !important;
  font-weight: 400;
}

.my-bread .el-breadcrumb__item .el-breadcrumb__separator {
  color: #848484 !important;
}
</style>
