<template>
  <div class="g-bd">
    <el-dialog
      v-if="recordDialog"
      :visible.sync="recordDialog"
      :append-to-body="true"
      class="custom-image-viewer"
      title="购买记录"
    >
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="name" label="购买日期" width="200">
          <template slot-scope="scope">
            {{ util.formatTime(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="value" label="购买类型">
          <template slot-scope="scope">
            {{ scope.row.method == 1 ? '支付宝' : '微信' }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="卡密" width="300">
          <template slot-scope="scope">
            {{ scope.row.activationCode }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="付款金额" width="130">
          <template slot-scope="scope">
            {{ scope.row.price }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="操作" width="130">
          <template slot-scope="scope">
            <el-button
              v-if="!scope.row.activationCode"
              @click="createCode(scope.row, scope.$index)"
              >生成卡密</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
      v-loading.fullscreen.lock="loading"
      v-if="showLogin"
      :visible.sync="showLogin"
      :append-to-body="true"
      title="短信验证码登录"
      width="400px"
    >
    <div id="captcha-element"></div>
      <el-form :model="loginForm">
        <el-form-item class="formItm">
          <el-input v-model="loginForm.username" placeholder="请输入手机号">
          </el-input>
        </el-form-item>
        <el-form-item class="formItm">
          <div class="spaceBetween">
            <el-input
              v-model="loginForm.validcode"
              placeholder="请输入短信验证码"
            ></el-input>
            <el-button class="smsBtn" type="primary" @click="onSendSms">
              {{ codeMsg }}
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <div>
        <el-button class="loginBtn" type="primary" @click="onLogin"
          >立即登录</el-button
        >
      </div>
    </el-dialog>
    <div class="safe_width_payCus">
      <div class="spaceBetween" style="padding: 20px 0px">
        <img class="logo" src="https://images.kkzhw.com/frontimg/pc_logo.png" />
        <div style="font-size: 16px" class="spaceStart">
          <div>
            <div v-if="userInfo.phone" class="spaceStart">
              <div class="logout" @click="loginOut">退出登录</div>
            </div>
            <div v-else class="login" @click="doShowLogin">登录</div>
          </div>
          <div class="record" @click="showRecord">购买记录</div>
        </div>
      </div>
      <div>
        <img
          src="https://images2.kkzhw.com/mall/images/20240909/mxtero_1725876083929.jpg"
        />
        <div class="downloadBox">
          <img
            class="downloadBtn"
            src="../../assets/download.png"
            @click="doDownload"
          />
        </div>
      </div>
      <div class="priceBox">
        <img class="linefoot" src="../../assets/linefoot.png" alt="" />
        <div class="spaceAround">
          <div
            v-for="(item, index) in itemList"
            :class="index == activeIndex ? 'item active' : 'item'"
            @click="chooseType(item, index)"
          >
            <div class="spaceStart">
              <img class="zs" src="../../assets/zs.png" />
              <div class="name">{{ item.name }}</div>
            </div>
            <div class="price">
              <span>{{ item.price }}</span>
              <span class="unit">元</span>
            </div>
          </div>
        </div>
      </div>
      <div class="spaceAround payBox">
        <div>
          <div
            :class="
              payIndex == 2 ? 'spaceStart payItem active' : 'spaceStart payItem'
            "
            @click="choosePay(2)"
          >
            <img
              v-show="payIndex == 2"
              class="selectedImg"
              src="../../assets/selected.png"
            />
            <img class="payImg" src="../../assets/zfb.png" />
            <div>支付宝支付</div>
          </div>
          <div
            :class="
              payIndex == 1 ? 'spaceStart payItem active' : 'spaceStart payItem'
            "
            style="margin-bottom: 0"
            @click="choosePay(1)"
          >
            <img
              v-show="payIndex == 1"
              class="selectedImg"
              src="../../assets/selected.png"
            />
            <img class="payImg" src="../../assets/wx.png" />
            <div>微信支付</div>
          </div>
        </div>
        <div>
          <canvas
            v-show="showQrCode"
            id="QRCode_header"
            style="width: 260px; height: 260px"
          ></canvas>
          <div v-show="!showQrCode" class="btn" @click="createPay">
            立即支付
          </div>
        </div>
      </div>
      <div class="totalBox">
        <div>
          扫码支付：<span class="total">{{ total }}</span
          >元
        </div>
      </div>
    </div>

    <el-dialog
      v-loading.fullscreen.lock="loading"
      v-if="showSuc"
      :visible.sync="showSuc"
      :append-to-body="true"
      title="购买成功"
      width="400px"
    >
      <div class="buySuc">
        <div class="tit">支付成功</div>
        <div class="note">您已经成功购买KK卡刀宏</div>
        <div class="code">卡密为：{{ activationCode }}</div>
        <div class="tips">如首次使用请您下载软件后打开注册进行使用</div>
        <!-- <div v-if="cardId == 3 || cardId == 4" class="wx">
          一对一调试联系作者微信：18777777763
        </div> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { loginCodenumApi, sendPhoneCode, logout } from '@/api/login';
import util from '@/utils/index';
import isLogin from '@/utils/isLogin';
import { mapState } from 'vuex';
import {
  getList,
  payMacro,
  payCheck,
  getMemberMacroOrderList,
  orderDetail,
  createCode,
} from '@/api/kkquse';
import QRCode from 'qrcode';
export default {
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
  },
  data() {
    return {
      isFlag:false,
      captchaIns: null,
      captchaButton: null,
      util,
      loading2: true,
      showSuc: false,
      loading: false,
      loginForm: {
        username: '',
        validcode: '',
      },
      codeMsg: '获取验证码',
      code: 60,
      isDis: false,
      captchaIns: null,
      showLogin: false,
      tableData: [],
      recordDialog: false,
      showQrCode: false,
      downloadLink: '',
      orderId: '',
      cardId: '1',
      total: '',
      activeIndex: 0,
      payIndex: 1,
      seller_id: '',
      itemList: [
        {
          name: '周卡',
          price: '6.6',
        },
        {
          name: '月卡',
          price: '18.8',
        },
        {
          name: '半年卡',
          price: '88',
        },
        {
          name: '尊享卡',
          price: '188',
        },
      ],
    };
  },
  mounted() {
    if (!isLogin()) {
      this.showLogin = true;
      this.initCaptcha();
    }
    this.getList();
  },
  beforeUnmount() {
    this.captchaButton = null;
    this.captchaIns.destroyCaptcha()
    // 必须删除相关元素，否则再次mount多次调用 initAliyunCaptcha 会导致多次回调 captchaVerifyCallback
    var mask = document.getElementById('aliyunCaptcha-mask');
    if (mask) {
      mask.remove();
    }
    var popup = document.getElementById('aliyunCaptcha-window-popup');
    if (popup) {
      popup.remove();
    }
  },
  methods: {
    getInstance(instance) {
      this.captchaIns = instance;
    },
    async captchaVerifyCallback(captchaVerifyParam) {
      // 1.向后端发起业务请求，获取验证码验证结果和业务结果
      // const result = await xxxx('http://您的业务请求地址', {
      //     captchaVerifyParam: captchaVerifyParam, // 验证码参数
      //     yourBizParam... // 业务参数
      // });
      // return {
      //  captchaResult: true, // 验证码验证是否通过，boolean类型，必选
      //  bizResult: true, // 业务验证是否通过，boolean类型，可选；若为无业务验证结果的场景，bizResult可以为空
      // }
      let captchaResult = false;
      let bizResult = false;
      try {
        const res = await sendPhoneCode({
          telephone: encodeURIComponent(this.loginForm.username),
          validate: encodeURIComponent(JSON.stringify(JSON.parse(captchaVerifyParam))),
        });
        if (res.code == 200) {
          bizResult = true;
          captchaResult = true;
          this.$message.success('验证码发送成功！');
          this.countDown();
        }
      } finally {
        // this.captchaIns.refresh();
      }
      return {
        captchaResult: captchaResult,
        bizResult: bizResult,
      }
    },
    // 验证通过后调用
    onBizResultCallback() {
      this.captchaIns.hide()
      console.log('onBizResultCallback');
      // this.countDown();
      // this.doSendSmsCode();
    },
    createCode(row, index) {
      const orderId = row.id;
      createCode({
        orderId,
      }).then((res) => {
        if (res.code == 200 && res.data && res.data.activationCode) {
          this.$set(
            this.tableData[index],
            'activationCode',
            res.data.activationCode
          );
          this.$message.success('卡密生成成功');
        }
      });
    },
    doSendSmsCode(data) {
      this.loading = true;
      sendPhoneCode({
        telephone: this.loginForm.username,
        validate: data.validate,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success('验证码发送成功！');
            this.countDown();
          }
          this.loading = false;
        })
        .finally(() => {
          this.captchaIns.refresh();
        });
    },
    loginOut() {
      logout().then((res) => {
        if (res.code == 200) {
          localStorage.removeItem('token');
          localStorage.removeItem('yximtoken');
          localStorage.removeItem('userInfo');
          localStorage.removeItem('userInfoExpire');
          localStorage.removeItem('contentData');
          localStorage.removeItem('gameListData');
          location.reload();
        }
      });
    },
    doShowLogin() {
      this.showLogin = true;
    },
    onSendSms() {
      if (this.isDis == true) {
        this.$message.error('请稍后重试');
        return;
      }
      var myreg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
      if (this.loginForm.username == '') {
        this.$message.error('请输入手机号码');
        return;
      }
      if (!myreg.test(this.loginForm.username)) {
        this.$message.error('请输入正确的手机号码');
        return false;
      }
      this.captchaIns && this.captchaIns.show();
    },
    onLogin() {
      if (!this.loginForm.validcode) {
        this.$message.error('请输入验证码');
        return false;
      }
      this.loading = true;
      loginCodenumApi({
        username: this.loginForm.username,
        authCode: this.loginForm.validcode,
      }).then((response) => {
        this.loading = false;
        if (response.code == 200) {
          this.loginSuc(response);
        }
      });
    },
    loginSuc(response) {
      var date = response.data;
      localStorage.setItem('token', date.token);
      this.$store.dispatch('SetToken', date.token);
      location.reload();
    },
    initCaptcha() {
      this.captchaButton = document.getElementById('captcha-button');

window.initAliyunCaptcha({
  SceneId: 'l34lhmeq', // 场景ID。根据步骤二新建验证场景后，您可以在验证码场景列表，获取该场景的场景ID
  prefix: 'b7wjf0', // 身份标。开通阿里云验证码2.0后，您可以在控制台概览页面的实例基本信息卡片区域，获取身份标
  mode: 'popup', // 验证码模式。popup表示要集成的验证码模式为弹出式。无需修改
  element: '#captcha-element', // 页面上预留的渲染验证码的元素，与原代码中预留的页面元素保持一致。
  button: '#captcha-button', // 触发验证码弹窗的元素。button表示单击登录按钮后，触发captchaVerifyCallback函数。您可以根据实际使用的元素修改element的值
  captchaVerifyCallback: this.captchaVerifyCallback, // 业务请求(带验证码校验)回调函数，无需修改
  onBizResultCallback: this.onBizResultCallback, // 业务请求结果回调函数，无需修改
  getInstance: this.getInstance, // 绑定验证码实例函数，无需修改
  slideStyle: {
    width: 360,
    height: 40,
  }, // 滑块验证码样式，支持自定义宽度和高度，单位为px。其中，width最小值为320 px
  language: 'cn', // 验证码语言类型，支持简体中文（cn）、繁体中文（tw）、英文（en）
});
      // window.initNECaptchaWithFallback(
      //   {
      //     captchaId: '3455bd8a6484410ea146980a113839aa',
      //     width: '320px',
      //     mode: 'popup',
      //     apiVersion: 2,
      //     onVerify: (err, data) => {
      //       if (err) return;
      //       this.doSendSmsCode(data);
      //     },
      //   },
      //   (instance) => {
      //     this.captchaIns = instance;
      //   }
      // );
    },
    showRecord() {
      if (isLogin()) {
        getMemberMacroOrderList().then((res) => {
          if (res.code == 200) {
            this.tableData = res.data;
            this.recordDialog = true;
          }
        });
      } else {
        this.showLogin = true;
      }
    },
    doDownload() {
      window.open(this.downloadLink);
    },
    getSellerIdFromSearch() {
      const params = new URLSearchParams(location.search);
      const sellerId = params.get('seller_id');
      return sellerId || 0;
    },
    getList() {
      this.seller_id = this.getSellerIdFromSearch();
      getList(this.seller_id).then((res) => {
        if (res.code == 200) {
          this.downloadLink = res.data.link;
          let tempList = res.data.payment_amount || [];
          this.itemList.forEach((ele, index) => {
            this.itemList[index].price = tempList[`amount${index + 1}`];
            this.itemList[index].cardId = index + 1;
          });
          this.cardId = this.itemList[0].cardId;
          this.total = this.itemList[0].price;
        }
      });
    },
    countDown() {
      this.code -= 1;
      if (this.code <= 0) {
        this.code = 60;
        this.codeMsg = '获取验证码';
        this.isDis = false;
        clearInterval(interval);
        return;
      }
      this.codeMsg = '重新获取' + this.code + 'S';
      this.isDis = true;
      var interval = setTimeout(() => {
        this.countDown();
      }, 1000);
    },
    resetPay() {
      this.showQrCode = false;
      this.stl = clearInterval(this.stl);
    },
    choosePay(index) {
      this.payIndex = index;
      this.resetPay();
    },
    chooseType(item, index) {
      this.resetPay();
      this.activeIndex = index;
      this.total = item.price;
      this.cardId = item.cardId;
    },
    startCheck() {
      this.stl = setInterval(() => {
        this.doCheck();
      }, 2000);
    },
    getOrderDetail() {
      orderDetail({
        orderId: this.orderId,
      }).then((res) => {
        if (res.code == 200) {
          this.resetPay();
          this.activationCode = res.data.activationCode;
          this.showSuc = true;
        }
      });
    },
    doCheck() {
      let data = {
        orderId: this.orderId,
      };
      payCheck(data).then((res) => {
        if (res.code == 200) {
          if (res.data == true) {
            this.getOrderDetail();
          }
        }
      });
    },
    createPay() {
      let data = {};
      data.cardId = this.cardId;
      data.method = this.payIndex;
      data.sellerId = this.seller_id;
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      payMacro(data)
        .then((res) => {
          if (res.code == 200) {
            this.orderId = res.data.orderId;
            this.creatQrCode(res.data.url);
            this.startCheck();
          }
        })
        .finally(() => {
          loading.close();
        });
    },
    creatQrCode(picUrl) {
      let opts = {
        errorCorrectionLevel: 'H', //容错级别
        type: 'image/png', //生成的二维码类型
        quality: 0.3, //二维码质量
        margin: 2, //二维码留白边距
        width: 260, //宽
        height: 260, //高
        text: picUrl, //二维码内容
        color: {
          dark: '#333333', //前景色
          light: '#fff', //背景色
        },
      };
      let msg = document.getElementById('QRCode_header');
      QRCode.toCanvas(msg, picUrl, opts, function (error) {
        if (error) {
          this.$message.error('二维码加载失败');
        }
      });
      this.showQrCode = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.buySuc {
  font-size: 16px;
  line-height: 30px;
  .tit {
  }
  .code {
  }
}
.logout {
  margin-left: 10px;
  cursor: pointer;
}
.login {
  cursor: pointer;
}
.loginBtn {
  width: 100%;
}
.formItm {
  margin-bottom: 10px;
}
.smsBtn {
  margin-left: 20px;
}
.g-bd {
  background-color: #000;
  color: #fff;
  min-height: 100%;
}
.safe_width_payCus {
  width: 900px;
  margin: 0 auto;
  .logo {
    width: 400px;
    height: 90px;
  }
}
.downloadBox {
  margin-top: -114px;
}
.downloadBtn {
  width: 242px;
  height: 60px;
  margin: 0 auto;
  display: block;
  cursor: pointer;
}
.item {
  cursor: pointer;
  width: 210px;
  height: 133px;
  background: url('../../assets/cs.png') no-repeat;
  padding: 20px;
}
.item:hover,
.item.active {
  width: 210px;
  height: 133px;
  background: url('../../assets/cs_a.png') no-repeat;
  padding: 20px;
}
.linefoot {
  width: 525px;
  margin: 0 auto;
  padding: 50px 0 20px;
  display: block;
}
.price {
  font-weight: 600;
  font-size: 36px;
  padding-right: 6px;
}
.unit {
  font-size: 18px;
}
.name {
  padding-left: 8px;
  font-size: 24px;
}
.priceBox {
  padding-bottom: 50px;
}
.payBox {
  font-size: 20px;
  align-items: flex-end;
  .btn {
    font-size: 18px;
    color: #fff;
    background: #f8ab27;
    padding: 14px 40px;
    border-radius: 12px;
    cursor: pointer;
  }
  .payItem {
    font-size: 22px;
    color: #fff;
    margin-bottom: 30px;
    border-radius: 14px;
    border: 1px solid #fff;
    padding: 16px 36px;
    cursor: pointer;
    position: relative;
    transition: all 0.3s;
    position: relative;
    .payImg {
      width: 34px;
      height: 34px;
      margin-right: 10px;
    }
    .selectedImg {
      position: absolute;
      top: -15px;
      right: -15px;
      width: 30px;
    }
  }
  .payItem.active {
    border-color: #56a741;
  }
}
.totalBox {
  padding: 20px 0 80px 130px;
  color: #fff;
  font-size: 26px;
  .total {
    color: rgb(251, 172, 53);
    font-weight: 600;
    font-size: 42px;
  }
}
.record {
  cursor: pointer;
  padding-left: 15px;
  color: rgb(248, 171, 39);
  font-weight: 600;
}
</style>
