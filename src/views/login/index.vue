<template>
  <div class="dark_container">
    <div class="loginHeadContent">
      <loginHead />
    </div>

    <div class="login-container">
      <headerKk :active-index="activeIndex" />

      <div class="safe_width_login">
        <div class="safe_width_Left_img">
          <div class="login_safe_width_icon login_safe_width_icon1">
            一键上架
          </div>
          <div class="login_safe_width_icon login_safe_width_icon2">
            超全号源
          </div>
          <div class="login_safe_width_icon login_safe_width_icon3">
            超低费率
          </div>
        </div>
        <el-form
          v-loading.fullscreen.lock="loading"
          ref="loginForm"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          auto-complete="on"
          label-position="left"
          element-loading-text="正在加载.."
          element-loading-background="rgba(236, 236, 236, 0.40)"
        >
          <!-- <div class="spaceAround loginHead_choose">
            <div
              :class="
                loginType == 1
                  ? 'loginHead_chooseItem active'
                  : 'loginHead_chooseItem'
              "
              @click="chooseType(1)"
            >
              密码登录
            </div>
            <div
              :class="
                loginType == 2
                  ? 'loginHead_chooseItem active'
                  : 'loginHead_chooseItem'
              "
              @click="chooseType(2)"
            >
              短信登录
            </div>
          </div> -->
          <div v-if="loginType === 1" class="leftDiv"></div>
          <div v-if="loginType === 0" class="rightDiv"></div>
          <div v-if="loginType === 1" class="rigthDivAtive">
            <div class="one"></div>
            <div class="two"></div>
          </div>
          <myTab
            :tabs="[
              {
                title: '密码登录',
                // imgTitle: '../../../static/imgs/login_password_title.svg',
              },

              {
                title: '短信登录',
                // imgTitle: '../../../static/imgs/login_text_messaging.svg',
              },
              {
                title: '',
              },
            ]"
            style="margin-top: 16rpx"
            border-class="tab-border"
            class="login_tab"
            @click="handleTabClick"
          >
            <template slot-scope="slot">
              <div style="padding: 40rpx 44rpx">
                <div class="loginForm_box">
                  <el-form-item prop="username" class="iptStyle">
                    <span class="svg-container">
                      <!-- <svg-icon icon-class="user" /> -->
                      <img
                        src="../../../static/imgs/login_iphone_icon.svg"
                        alt=""
                        style="width: 36px"
                      />
                    </span>
                    <el-input
                      v-model="loginForm.username"
                      style="color: #1b1b1b"
                      type="text"
                      placeholder="请输入手机号码"
                    />
                  </el-form-item>

                  <el-form-item
                    v-if="slot.data == 0"
                    prop="password"
                    class="iptStyle"
                  >
                    <span class="svg-container">
                      <img
                        src="../../../static/imgs/login_password_icon.svg"
                        alt=""
                        style="width: 36px"
                      />
                      <!-- <svg-icon icon-class="password" /> -->
                    </span>
                    <el-input
                      :type="pwdType"
                      v-model="loginForm.password"
                      style="color: #1b1b1b"
                      name="password"
                      placeholder="请输入登录密码"
                      @keyup.enter.native="handleLogin"
                    />
                    <span class="show-pwd" @click="showPwd">
                      <svg-icon
                        :class="pwdType === 'password' ? '' : 'password'"
                        :icon-class="
                          pwdType === 'password' ? 'eye' : 'eye-open'
                        "
                      />
                    </span>
                  </el-form-item>
                  <div id="captcha-element"></div>
                  <el-form-item
                    v-if="slot.data == 1"
                    prop="validcode"
                    class="iptStyle"
                  >
                    <span class="svg-container">
                      <!-- <svg-icon icon-class="user" /> -->
                      <img
                        src="../../../static/imgs/login_password_icon.svg"
                        alt=""
                        style="width: 36px"
                      />
                    </span>
                   
                    <el-input
                      v-model="loginForm.validcode"
                      name="validcode"
                      type="text"
                      placeholder="请输入验证码"
                     
                      style="width: 55%; color: #333"
                      @keyup.enter.native="handleLogin"
                    />
                    <div  class="code_wrap" @click="sendCode">
                      {{ codeMsg }}
                    </div>
                  </el-form-item>

                  <div class="spaceBetween forget_wrap">
                    <router-link to="/forgetPwd" rel="nofollow"
                      >忘记密码？</router-link
                    >
                    <router-link to="/regin" rel="nofollow">注册</router-link>
                  </div>

                  <el-button
                    :loading="loading"
                    :class="buttonClass"
                    type="primary"
                    style="
                      font-family: PingFang SC;
                      font-size: 16px;
                      border: 1px solid #ffddbe;
                    "
                    @click.native.prevent="handleLogin"
                  >
                    立即登录
                  </el-button>
                </div>
              </div>
            </template>
          </myTab>
        </el-form>
      </div>
    </div>
    <!-- <div class="footerCt"> -->

    <div style="background: #fffcf8">
      <footerKk />
    </div>

    <!-- </div> -->
  </div>
</template>

<script>
import '@/utils/yidun-captcha';
import footerKk from '@/components/footerKk/index';
import loginHead from '@/components/loginHead/index';
import headerKk from '@/components/headerKk/header';
import myTab from '@/components/myTab/index';
import { loginPwdnumApi, loginCodenumApi, sendPhoneCode } from '@/api/login';
// import md5 from 'js-md5';
// import sha1 from 'js-sha1';
export default {
  components: {
    footerKk,
    loginHead,
    headerKk,
    myTab,
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (value == '') {
        callback(new Error('请输入正确的登录手机号'));
      } else {
        callback();
      }
    };
    const validatePass = (rule, value, callback) => {
      if (value.length < 5) {
        callback(new Error('密码不能小于5位'));
      } else {
        callback();
      }
    };
    const validateCode = (rule, value, callback) => {
      if (value.length != 6) {
        callback(new Error('验证码不能小于6位'));
      } else {
        callback();
      }
    };

    return {
      // 17788072597
      isFlag:false,
      captchaIns: null,
      captchaButton: null,
      data: 2,
      loginType: 0,
      activeIndex: 7,
      codeMsg: '获取验证码',
      code: 60,
      isDis: false,
      codeNum: '',
      loginForm: {
        username: '',
        password: '',
        validcode: '',
      },
      loginRules: {
        username: [
          {
            required: true,
            trigger: 'blur',
            validator: validateUsername,
          },
        ],
        password: [
          {
            required: true,
            trigger: 'blur',
            validator: validatePass,
          },
        ],
        validcode: [
          {
            required: true,
            trigger: 'blur',
            validator: validateCode,
          },
        ],
      },
      loading: false,
      pwdType: 'password',
      redirect: undefined,
    };
  },
  computed: {
    buttonClass() {
      return [
        'loginBtn',
        'btn_padding',
        (this.loginForm.username && this.loginForm.password) ||
        (this.loginForm.username && this.loginForm.validcode)
          ? 'loginBtnActive'
          : 'loginBtnDefalut',
      ];
    },
  },
  watch: {
    // $route: {
    // 	handler: function(route) {
    // 		this.redirect = route.query && route.query.redirect
    // 	},
    // 	immediate: true
    // }
  },
  created() {
    if (this.$route.query.redirect) {
      this.redirect = decodeURIComponent(this.$route.query.redirect);
    }
  },
  mounted() {
    this.initCaptcha();
  },
  beforeUnmount() {
    this.captchaButton = null;
    this.captchaIns.destroyCaptcha()
    // 必须删除相关元素，否则再次mount多次调用 initAliyunCaptcha 会导致多次回调 captchaVerifyCallback
    var mask = document.getElementById('aliyunCaptcha-mask');
    if (mask) {
      mask.remove();
    }
    var popup = document.getElementById('aliyunCaptcha-window-popup');
    if (popup) {
      popup.remove();
    }
  },
  methods: {
    getInstance(instance) {
      this.captchaIns = instance;
    },
    async captchaVerifyCallback(captchaVerifyParam) {
      // 1.向后端发起业务请求，获取验证码验证结果和业务结果
      // const result = await xxxx('http://您的业务请求地址', {
      //     captchaVerifyParam: captchaVerifyParam, // 验证码参数
      //     yourBizParam... // 业务参数
      // });
      // return {
      //  captchaResult: true, // 验证码验证是否通过，boolean类型，必选
      //  bizResult: true, // 业务验证是否通过，boolean类型，可选；若为无业务验证结果的场景，bizResult可以为空
      // }
      let captchaResult = false;
      let bizResult = false;
      try {
        const res = await sendPhoneCode({
          telephone: encodeURIComponent(this.loginForm.username),
          validate: encodeURIComponent(JSON.stringify(JSON.parse(captchaVerifyParam))),
        });
        if (res.code == 200) {
          bizResult = true;
          captchaResult = true;
          this.$message.success('验证码发送成功！');
          this.countDown();
        }
      } finally {
        // this.captchaIns.refresh();
      }
      return {
        captchaResult: captchaResult,
        bizResult: bizResult,
      }
    },
    // 验证通过后调用
    onBizResultCallback() {
      this.captchaIns.hide()
      console.log('onBizResultCallback');
      // this.countDown();
      // this.doSendSmsCode();
    },
    handleTabClick(e) {
      if (e == 1 && !this.captchaIns) {
        this.initCaptcha();
      }
      this.loginType = e;
    },
    initCaptcha() {
      this.captchaButton = document.getElementById('captcha-button');

      window.initAliyunCaptcha({
        SceneId: 'l34lhmeq', // 场景ID。根据步骤二新建验证场景后，您可以在验证码场景列表，获取该场景的场景ID
        prefix: 'b7wjf0', // 身份标。开通阿里云验证码2.0后，您可以在控制台概览页面的实例基本信息卡片区域，获取身份标
        mode: 'popup', // 验证码模式。popup表示要集成的验证码模式为弹出式。无需修改
        element: '#captcha-element', // 页面上预留的渲染验证码的元素，与原代码中预留的页面元素保持一致。
        button: '#captcha-button', // 触发验证码弹窗的元素。button表示单击登录按钮后，触发captchaVerifyCallback函数。您可以根据实际使用的元素修改element的值
        captchaVerifyCallback: this.captchaVerifyCallback, // 业务请求(带验证码校验)回调函数，无需修改
        onBizResultCallback: this.onBizResultCallback, // 业务请求结果回调函数，无需修改
        getInstance: this.getInstance, // 绑定验证码实例函数，无需修改
        slideStyle: {
          width: 360,
          height: 40,
        }, // 滑块验证码样式，支持自定义宽度和高度，单位为px。其中，width最小值为320 px
        language: 'cn', // 验证码语言类型，支持简体中文（cn）、繁体中文（tw）、英文（en）
      });
      // window.initNECaptchaWithFallback(
      //   {
      //     captchaId: '3455bd8a6484410ea146980a113839aa',
      //     width: '320px',
      //     mode: 'popup',
      //     apiVersion: 2,
      //     onVerify: (err, data) => {
      //       if (err) return;
      //       this.doSendSmsCode(data);
      //     },
      //   },
      //   (instance) => {
      //     this.captchaIns = instance;
      //   }
      // );
    },

    chooseType(num) {
      if (num == 2 && !this.captchaIns) {
        this.initCaptcha();
      }
      this.loginType = num;
    },
    showPwd() {
      if (this.pwdType === 'password') {
        this.pwdType = '';
      } else {
        this.pwdType = 'password';
      }
    },
    /**
     * 获取验证码
     */
    sendCode() {
      if (this.isDis == true) {
        this.$message.error('请稍后重试');
        return;
      }
      var myreg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
      if (this.loginForm.username == '') {
        this.$message.error('请输入手机号码');
        return;
      }
      if (!myreg.test(this.loginForm.username)) {
        this.$message.error('请输入正确的手机号码');
        return false;
      }
      console.log('执行了',this.captchaIns);
      this.captchaIns && this.captchaIns.show()
    },
    doSendSmsCode(data) {
      this.loading = true;
      sendPhoneCode({
        telephone: this.loginForm.username,
        validate: data.validate,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success('验证码发送成功！');
            this.countDown();
          }
          this.loading = false;
        })
        .finally(() => {
          this.captchaIns.refresh();
        });
    },
    /**
     * 倒计时
     */
    countDown() {
      this.code -= 1;
      if (this.code == 0) {
        this.code = 60;
        this.codeMsg = '获取验证码';
        this.isDis = false;
        clearInterval(interval);
        return;
      }
      this.codeMsg = '重新获取' + this.code + 'S';
      this.isDis = true;
      var _this = this;
      var interval = setTimeout(function () {
        _this.countDown();
      }, 1000);
    },
    loginSuc(response) {
      var date = response.data;
      localStorage.setItem('token', date.token);
      this.$store.dispatch('SetToken', date.token);
      if (!this.redirect) {
        this.$router.push({
          path: '/',
        });
      } else {
        window.open(this.redirect, '_self');
      }
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          if (this.loginType == 0) {
            this.loading = true;
            loginPwdnumApi({
              username: this.loginForm.username,
              password: this.loginForm.password,
            }).then((response) => {
              this.loading = false;
              if (response.code == 200) {
                this.loginSuc(response);
              }
            });
          } else {
            if (!this.loginForm.validcode) {
              this.$message.error('请输入验证码');
              return false;
            }
            this.loading = true;
            loginCodenumApi({
              username: this.loginForm.username,
              authCode: this.loginForm.validcode,
            }).then((response) => {
              this.loading = false;
              if (response.code == 200) {
                this.loginSuc(response);
              }
            });
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
$bg: #2d3a4b;
$light_gray: #eee;
/* reset element-ui css */

.login-container {
  background: linear-gradient(
    180deg,
    #fff1e2 16.51%,
    #fff9f3 44.06%,
    #fff9f3 74.95%,
    #ffe1c3 100%
  );
  .el-input {
    display: inline-block;
    height: 55px;
    width: 85%;
    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      // padding: 0px 5px 12px 15px;
      color: #333;
      height: 47px;
      &:-webkit-autofill {
        -webkit-box-shadow: none !important;
        -webkit-text-fill-color: #000 !important;
      }
    }
  }
  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
    margin-bottom: 30px;
  }
}
</style>

<style rel="stylesheet/scss" lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #909090;
$light_gray: #eee;

$light_graynEW: #ff6716;

.loginHeadContent {
  width: 100%;
  background: #fff;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  position: relative;
}
.leftDiv {
  position: absolute;
  width: 50px;
  height: 80px;
  top: -0px;
  left: 0px;
  background: #f0edea;
  border-top-left-radius: 24px;
  z-index: 1;
}
.rightDiv {
  position: absolute;
  width: 32px;
  height: 180px;
  top: 0px;
  right: 123px;
  background: #f0edea;
  border-top-right-radius: 24px;
  z-index: 1;
}
// .rigthDivAtive {
//   position: absolute;
//   width: 32px;
//   height: 180px;
//   top: -30px;
//   right: 71px;
//   // background: red;
//   border-top-right-radius: 28px;
//   z-index: 1;

//   .one {
//     width: 50px;
//     height: 50px;
//     background: #fff;
//   }
//   .two {
//     // background: #fdf5ed;
//     width: 78px;
//     height: 81px;
//     background: red;
//     position: absolute;
//     top: -29px;
//     left: -1px;
//     border-radius: 50px;
//   }
// }
.safe_width_login {
  width: 1200px;
  margin: 0 auto;
  display: flex;
  // align-items: center;
  justify-content: space-between;
  padding-bottom: 80px;
}
.safe_width_Left_img {
  width: 362px;
  height: 377px;
  margin-left: 152px;
  background: url(../../../static/imgs/login_main.png) no-repeat center top;
  background-size: cover;
  position: relative;
  top: 40.06px;
}
.login_safe_width_icon {
  width: 181px;
  height: 78px;
  background-position: center top;
  background-size: cover;
  position: absolute;
  // text-align: center;
  line-height: 78px;
  font-family: YouSheBiaoTiHei;
  font-size: 20px;
  text-indent: 83px;
}

.login_safe_width_icon1 {
  top: 49px;
  left: -158px;
  background-image: url(../../../static/imgs/login_icon1.svg);
}

.login_safe_width_icon2 {
  top: 124.94px;
  right: -150px;
  background-image: url(../../../static/imgs/login_icon2.svg);
}

.login_safe_width_icon3 {
  // bottom: 0px;
  top: 274px;
  left: -51.5px;
  background-image: url(../../../static/imgs/login_icon3.svg);
}

.code_wrap {
  float: right;
  height: 48px;
  // margin-right: 25.6px;
  color: #ff720c;
  font-family: Inter;
  font-size: 15.4px;
  font-style: normal;
  font-weight: 400;
  border: none;
  cursor: pointer;
  text-align: center;
  line-height: 44px;
  border-radius: 20px;
  margin-top: 7px;
}
.login-container {
  overflow: hidden;
  width: 100%;

  .login-form {
    float: right;
    margin-top: 20px;
    width: 478px;
    max-width: 100%;
    // background: #fff;
    box-sizing: border-box;
    border-radius: 10px;
    position: relative;
  }
  // .login-form /deep/
  .loginHead_choose {
    font-size: 20px;
    color: #999;
    font-weight: 500;
    border-bottom: 1px solid #eeeeee;
  }
  .loginHead_chooseItem {
    border-bottom: 2px solid transparent;
    padding: 18px 2px;
    cursor: pointer;
    transition: all 0.3s;
  }
  .loginHead_chooseItem.active {
    border-color: #ff6716;
    color: #ff6716;
  }
  .loginForm_box {
    width: 100%;
    box-sizing: border-box;
    padding: 39px 41px 40px 39px;
  }
  .iptStyle {
    border: 1px solid rgba(150, 150, 150, 0.4);
    padding: 0px;
    height: 57px;
    padding: 0 20px !important;
    background: #fff;
    // border: none;
    // background: url(../../../static/imgs/border_medium_big_bk.png);
    // background-size: 100% 100%;
    // padding: 0px;
    border-radius: 50px;
    /deep/.el-input__inner {
      height: 57px;
      padding: 0px;
      margin-left: 8px;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      letter-spacing: 0.56px;
    }
    /deep/ .el-form-item__content {
      // line-height: 35px!important;
      .svg-container {
        margin-top: -5px;
      }
      .show-pwd {
        top: 6px;
      }
    }
    /deep/ .el-form-item__error {
      // background: radial-gradient(
      //     31.25% 236.33% at 96.59% 31.25%,
      //     rgba(255, 255, 255, 0.1) 0%,
      //     rgba(255, 255, 255, 0) 100%
      //   ),
      //   radial-gradient(
      //     39.2% 181% at 5.68% 100%,
      //     rgba(246, 251, 34, 0.306) 0%,
      //     rgba(255, 158, 69, 0) 100%
      //   ),
      //   radial-gradient(
      //     71.74% 117.31% at 32.95% 0%,
      //     rgba(255, 137, 137, 0.828) 21.25%,
      //     rgba(255, 169, 106, 0.513) 88.62%
      //   ),
      //   radial-gradient(
      //     92.05% 200% at 94.89% -132.81%,
      //     #ff7a00 67.59%,
      //     rgba(255, 199, 0, 0.38) 100%
      //   ),
      //   linear-gradient(0deg, #fff500, #fff500);
      // color: transparent;
      // -webkit-background-clip: text; /* background-clip: text; */
      color: #ff720c;
      height: 20px;
      font-family: 'PingFang SC';

      padding: 0px;
      margin-left: 34px;
      font-weight: 400;
      margin-top: 8px;
    }
  }
  .iptStyle:focus-within {
    /* 在这里添加你想要修改的样式，例如边框样式 */
    // border: 2px solid transparent !important;
    // border-radius: 50px !important;

    // background-clip: padding-box, border-box !important;

    // background-origin: padding-box, border-box !important;
    // background-image: linear-gradient(to right, #fff, #fff),
    //   linear-gradient(
    //     180deg,
    //     rgba(255, 122, 0, 0.6) 8%,
    //     rgba(255, 199, 0, 0.6) 20%
    //   ) !important;
    border: none;
    background: url(../../../static/imgs/border_medium_big_bk.png);
    background-size: 100% 100%;
    padding-top: 1px !important;
    padding-left: 21px !important;

    // padding: 2px;
  }
  .is-error {
    // border: 2px solid transparent !important;
    // border-radius: 50px !important;

    // background-clip: padding-box, border-box !important;

    // background-origin: padding-box, border-box !important;
    // background-image: linear-gradient(to right, #fff, #fff),
    //   linear-gradient(
    //     180deg,
    //     rgba(255, 122, 0, 0.6) 8%,
    //     rgba(255, 199, 0, 0.6) 20%
    //   ) !important;
    // height: 59px;
    border: none;
    background: url(../../../static/imgs/border_medium_big_bk.png);
    // background: var(--btn-background-gradient);
    // position: relative;
    // z-index: 1;
    background-size: 100% 100%;
    padding-top: 1px !important;
    padding-left: 21px !important;
    // height: 61px !important;

    // padding: 2px !important;
  }

  // .is-error::before {
  //   content: '';
  //   position: absolute;
  //   width: 100%;
  //   top: 0;
  //   left: 0;
  //   right: 0;
  //   bottom: 0;
  //   z-index: 2;
  //   margin: 2px;
  //   height: 53px;
  //   background: #fff !important;
  //   border: none;
  //   border-radius: 50px;
  // }
  .loginBtn {
    width: 100%;
    margin-top: 20px;
    // padding: 18px 0;
    width: 162px;
    height: 50px;
    border-radius: 50px;
    margin-left: 50%;
    transform: translateX(-50%);
  }

  .forget_wrap {
    font-size: 14px;
    font-family: 'PingFang SC';
    color: #969696;
    font-weight: 400;
    margin-top: 19px;
    letter-spacing: 0.56px;
  }

  .svg-container {
    // padding: 6px 5px 6px 15px;
    color: #909090;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .show-pwd {
    position: absolute;
    right: 0px;
    top: 6px;
    font-size: 16px;
    color: #909090;
    cursor: pointer;
    user-select: none;
    .svg-icon {
      width: 21.913px;
      height: 11px;
    }
    .password {
      width: 21px;
      height: 21px;
      margin-top: 13px;
    }
  }
  // @media screen and (min-width: 1290px) {
  //   .login_textWrap {
  //     margin-top: -320px;
  //     width: 520px;
  //     font-size: 36px;
  //   }
  //   .login-form {
  //     left: 64%;
  //     // width: 381.6px;
  //     width: 408.7px;
  //   }
  //   .title {
  //     font-size: 36px;
  //   }
  // }
}
::v-deep .tab-head {
  width: 366px !important;
}
::v-deep .tab-btn:nth-child(3) {
  position: absolute;
  .right {
    &::after {
      background: red !important;
      // background: red;
      // height: 36px;
      border: none;
    }
  }
}
::v-deep .tab-btn:nth-child(2) {
  .right {
    &::after {
      background: #fdf5ed !important;
      // background: red;
      // height: 36px;
      border: none;
    }
  }
}
::v-deep .tab-btn {
  font-size: 26px !important;
  letter-spacing: 0.56px;
  color: #969696 !important;
  border: none !important;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24px;

  .right {
    &::before {
      // background: #ffddbe;
    }
    &::after {
      background: #fff;
      // background: red;
      // height: 36px;
      border: none;
    }
  }
  .left {
    &::before {
      // background: #ffddbe;
    }
    &::after {
      background: #ffffff;
      border: none;
    }
  }
}
::v-deep .selected {
  border: none;
  padding-top: 18px !important;
  // color: #ff6716 !important;
  bottom: 0px !important;
  span {
    background: var(--btn-background-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
  // padding-top: 0px !important;
  // display: flex;
  // align-items: end;
  // justify-items: center;
  // color: red !important;
}
::v-deep .tab-body {
  border: none;
  z-index: 99;
  border-radius: 24px;
}
.dark_container {
  width: 100%;
  // background: #fdf5ed;
  background: linear-gradient(180deg, #fdf5ed 22.61%, #fff 42.46%, #fdf5ed 53%);
}
::v-deep .tab-btn {
  .textBtn {
    // margin-top: -8px;
    width: 96px;
  }
}
</style>
