<template>
  <div class="dark_container">
    <div class="reginBk">
      <div class="loginHeadContent">
        <loginHead />
      </div>
      <headerKk :active-index="activeIndex" />

      <div
        class="safe_width"
        style="
          border-radius: 24px;
          overflow: hidden;
          margin-top: 20px;

          background: #fff;
          margin-bottom: 80px;
          padding: 40px;
        "
      >
        <div class="regin_head spaceBetween">
          <div class="spaceStart">
            <!-- <i class="el-icon-user"></i>&nbsp;&nbsp; -->
            <span class="forgotPassword">注册账号</span>
          </div>
          <div class="accountText spaceStart login_n">
            <div>已有账号？</div>
            <router-link
              to="/login"
              class="forgotPassword_right_text"
              rel="nofollow"
              >立即登录</router-link
            >
          </div>
        </div>
        <div class="forgetUnderscore">
          <div class="forgetUnderscoreContent"></div>
        </div>

        <div class="page_comStyle_login_box">
          <!-- <img class="formBk" src="../../../static/imgs/logo_Bk.png" alt="" /> -->
          <el-form
            v-loading.fullscreen.lock="loading"
            ref="loginForm"
            :model="loginForm"
            :rules="loginRules"
            class="loginFormStyle"
            label-width="130px"
            style="margin-top: 40px"
            auto-complete="on"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
          >
            <div class="reginForm_box">
              <el-form-item
                class="forget-pwd-input"
                prop="phone"
                label="手机号"
              >
                <el-input
                  v-model="loginForm.phone"
                  style="color: #1b1b1b"
                  type="text"
                  placeholder="请输入手机号码"
                />
              </el-form-item>

              <el-form-item
                class="forget-pwd-input"
                prop="password"
                label="密码"
              >
                <el-input
                  :type="pwdType"
                  v-model="loginForm.password"
                  style="color: #1b1b1b"
                  placeholder="请输入登录密码"
                />
                <span class="show-pwdR" @click="showPwd">
                  <svg-icon
                    :class="pwdType === 'password' ? '' : 'password1'"
                    :icon-class="pwdType === 'password' ? 'eye' : 'eye-open'"
                  />
                </span>
              </el-form-item>

              <el-form-item
                class="forget-pwd-input"
                prop="password"
                label="确认密码"
              >
                <el-input
                  :type="pwdTypeTwo"
                  v-model="loginForm.password2"
                  style="color: #1b1b1b"
                  placeholder="请输入登录密码"
                />
                <span class="show-pwdR" @click="showPwdTwo">
                  <svg-icon
                    :class="pwdTypeTwo === 'password' ? '' : 'password2'"
                    :icon-class="pwdTypeTwo === 'password' ? 'eye' : 'eye-open'"
                  />
                </span>
              </el-form-item>

              <!-- <el-form-item prop="" label="邀请码">
              <el-input
                v-model="loginForm.invite_code"
                style="color: #333"
                type="text"
                placeholder="请输入邀请码(选填)"
              />
            </el-form-item> -->
            <div id="captcha-element"></div>
              <el-form-item
                class="forget-pwd-input"
                prop="code"
                label="短信验证码"
              >
                <el-input
                  v-model="loginForm.code"
                  type="text"
                  placeholder="请输入验证码"
                  style="color: #1b1b1b"
                />
                <div
                  style="margin-right: 13px"
                  class="code_wrap posionRight"
                  @click="sendCode"
                >
                  {{ codeMsg }}
                </div>
              </el-form-item>

              <el-button
                :loading="loading"
                :class="buttonClass"
                type="primary"
                @click.native.prevent="handleLogin"
              >
                同意协议并注册
              </el-button>
              <div style="padding-top: 20px" class="spaceCenter agreeRegin">
                <!-- <el-checkbox
                v-model="checked"
                style="margin-right: 6px"
              ></el-checkbox> -->
                <!-- <IconFont
                v-if="!checked"
                :size="20"
                style="margin-right: 8px; cursor: pointer"
                icon="unchecked"
                @click="changChecked(true)"
              /> -->
                <div
                  v-if="!checked"
                  style="
                    width: 20px;
                    height: 20px;
                    border-radius: 3px;
                    border: 2px solid #969696;
                    margin-right: 8px;
                    cursor: pointer;
                    margin-top: -2px;
                  "
                  icon="unchecked"
                  @click="changChecked(true)"
                ></div>
                <IconFont
                  v-if="checked"
                  :size="20"
                  style="
                    margin-right: 8px;
                    cursor: pointer;
                    color: #ff720c;
                    width: 20px;
                    height: 20px;
                  "
                  icon="checked"
                  @click="changChecked(false)"
                />
                <div
                  style="
                    font-size: 14px;
                    font-family: PingFang SC;
                    letter-spacing: 0.56px;
                    color: rgba(0, 0, 0, 0.4);
                  "
                >
                  我已阅读并同意
                  <span class="agree" @click="agreeMentFun"
                    >《看看账号网用户协议》</span
                  >
                </div>
              </div>
              <div v-if="checkedFlag" class="spaceCenter footerAgreementText">
                请阅读并同意登录注册协议～
              </div>
              <!-- <div class="spaceCenter footerAgreementText"></div> -->
            </div>
          </el-form>
        </div>
      </div>
    </div>

    <div class="footerCt">
      <footerKk />
    </div>
  </div>
</template>

<script>
import footerKk from '@/components/footerKk/index';
import loginHead from '@/components/loginHead/index';
import headerKk from '@/components/headerKk/header';
import { sendPhoneCode, registerApi } from '@/api/login';
import '@/utils/yidun-captcha';

export default {
  components: {
    footerKk,
    loginHead,
    headerKk,
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (value == '') {
        callback(new Error('请输入登录手机号'));
      } else {
        callback();
      }
    };
    const validatePass = (rule, value, callback) => {
      if (value.length < 5) {
        callback(new Error('密码不能小于5位'));
      } else {
        callback();
      }
    };
    const validateCode = (rule, value, callback) => {
      if (value.length != 6) {
        callback(new Error('验证码为6位'));
      } else {
        callback();
      }
    };

    return {
      isFlag:false,
      captchaIns: null,
      captchaButton: null,
      // 17788072597
      loginType: 1,
      activeIndex: 7,
      codeMsg: '获取验证码',
      code: 60,
      isDis: false,
      checkedFlag: false,
      codeNum: '',
      loginForm: {
        phone: '',
        password: '',
        password2: '',
        code: '',
        // invite_code: '',
        platform: 5,
      },
      loginRules: {
        phone: [
          {
            required: true,
            trigger: 'blur',
            validator: validateUsername,
          },
        ],
        password: [
          {
            required: true,
            trigger: 'blur',
            validator: validatePass,
          },
        ],
        code: [
          {
            required: true,
            trigger: 'blur',
            validator: validateCode,
          },
        ],
      },
      loading: false,
      pwdType: 'password',
      pwdTypeTwo: 'password',
      redirect: undefined,
      checked: false,
    };
  },
  computed: {
    buttonClass() {
      const emptyFields = Object.keys(this.loginForm).filter((key) => {
        const value = this.loginForm[key];
        return value === null || value === undefined || value === '';
      });

      return [
        'loginBtnPwd',
        'reginBtn',
        'btn_padding',
        // 'loginBtnActive2x',
        emptyFields.length > 0 ? 'btnDefalut2x' : 'btnActive2x',
      ];
    },
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  mounted() {
    this.initCaptcha();
  },
  beforeUnmount() {
    this.captchaButton = null;
    this.captchaIns.destroyCaptcha()
    // 必须删除相关元素，否则再次mount多次调用 initAliyunCaptcha 会导致多次回调 captchaVerifyCallback
    var mask = document.getElementById('aliyunCaptcha-mask');
    if (mask) {
      mask.remove();
    }
    var popup = document.getElementById('aliyunCaptcha-window-popup');
    if (popup) {
      popup.remove();
    }
  },
  methods: {
    getInstance(instance) {
      this.captchaIns = instance;
    },
    async captchaVerifyCallback(captchaVerifyParam) {
      // 1.向后端发起业务请求，获取验证码验证结果和业务结果
      // const result = await xxxx('http://您的业务请求地址', {
      //     captchaVerifyParam: captchaVerifyParam, // 验证码参数
      //     yourBizParam... // 业务参数
      // });
      // return {
      //  captchaResult: true, // 验证码验证是否通过，boolean类型，必选
      //  bizResult: true, // 业务验证是否通过，boolean类型，可选；若为无业务验证结果的场景，bizResult可以为空
      // }
      let captchaResult = false;
      let bizResult = false;
      try {
        const res = await sendPhoneCode({
          telephone: encodeURIComponent(this.loginForm.phone),
          validate: encodeURIComponent(JSON.stringify(JSON.parse(captchaVerifyParam))),
        });
        if (res.code == 200) {
          bizResult = true;
          captchaResult = true;
          this.$message.success('验证码发送成功！');
          this.countDown();
        }
      } finally {
        // this.captchaIns.refresh();
      }
      return {
        captchaResult: captchaResult,
        bizResult: bizResult,
      }
    },
    // 验证通过后调用
    onBizResultCallback() {
      this.captchaIns.hide()
      console.log('onBizResultCallback');
      // this.countDown();
      // this.doSendSmsCode();
    },
    initCaptcha() {
      this.captchaButton = document.getElementById('captcha-button');

      window.initAliyunCaptcha({
        SceneId: 'l34lhmeq', // 场景ID。根据步骤二新建验证场景后，您可以在验证码场景列表，获取该场景的场景ID
        prefix: 'b7wjf0', // 身份标。开通阿里云验证码2.0后，您可以在控制台概览页面的实例基本信息卡片区域，获取身份标
        mode: 'popup', // 验证码模式。popup表示要集成的验证码模式为弹出式。无需修改
        element: '#captcha-element', // 页面上预留的渲染验证码的元素，与原代码中预留的页面元素保持一致。
        button: '#captcha-button', // 触发验证码弹窗的元素。button表示单击登录按钮后，触发captchaVerifyCallback函数。您可以根据实际使用的元素修改element的值
        captchaVerifyCallback: this.captchaVerifyCallback, // 业务请求(带验证码校验)回调函数，无需修改
        onBizResultCallback: this.onBizResultCallback, // 业务请求结果回调函数，无需修改
        getInstance: this.getInstance, // 绑定验证码实例函数，无需修改
        slideStyle: {
          width: 360,
          height: 40,
        }, // 滑块验证码样式，支持自定义宽度和高度，单位为px。其中，width最小值为320 px
        language: 'cn', // 验证码语言类型，支持简体中文（cn）、繁体中文（tw）、英文（en）
      });
      // window.initNECaptchaWithFallback(
      //   {
      //     captchaId: '3455bd8a6484410ea146980a113839aa',
      //     width: '320px',
      //     mode: 'popup',
      //     apiVersion: 2,
      //     onVerify: (err, data) => {
      //       if (err) return;
      //       this.doSendSmsCode(data);
      //     },
      //   },
      //   (instance) => {
      //     this.captchaIns = instance;
      //   }
      // );
    },
    agreeMentFun() {
      this.$router.push({
        path: '/helpCenter?id=65',
      });
    },
    showPwd() {
      if (this.pwdType === 'password') {
        this.pwdType = '';
      } else {
        this.pwdType = 'password';
      }
    },
    showPwdTwo() {
      if (this.pwdTypeTwo === 'password') {
        this.pwdTypeTwo = '';
      } else {
        this.pwdTypeTwo = 'password';
      }
    },
    /**
     * 获取验证码
     */
    sendCode() {
      if (this.isDis == true) {
        this.$message.error('请稍后重试');
        return;
      }
      var myreg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
      if (this.loginForm.phone == '') {
        this.$message.error('请输入手机号码');
        return;
      }
      if (!myreg.test(this.loginForm.phone)) {
        this.$message.error('请输入正确的手机号码');
        return false;
      }
      this.captchaIns && this.captchaIns.show();
    },
    doSendSmsCode(data) {
      this.loading = true;
      sendPhoneCode({
        telephone: this.loginForm.phone,
        validate: data.validate,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success('验证码发送成功！');
            this.countDown();
          }
          this.loading = false;
        })
        .finally(() => {
          this.captchaIns.refresh();
        });
    },
    /*
     * 倒计时
     */
    countDown() {
      this.code -= 1;
      if (this.code == 0) {
        this.code = 60;
        this.codeMsg = '获取验证码';
        this.isDis = false;
        clearInterval(interval);
        return;
      }
      this.codeMsg = '重新获取' + this.code + 'S';
      this.isDis = true;
      var _this = this;
      var interval = setTimeout(function () {
        _this.countDown();
      }, 1000);
    },
    //协议选择框
    changChecked(flag) {
      this.checked = flag;
      if (flag) {
        this.checkedFlag = false;
      }
    },
    // 提交注册-验证
    handleLogin() {
      if (!this.checked) {
        // this.$message.error('请阅读并同意登录注册协议~');
        this.checkedFlag = true;
        return;
      }
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.reginFun();
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    reginFun() {
      if (this.loginForm.password != this.loginForm.password2) {
        this.$message.error('两次输入密码不一致~');
        return;
      }
      this.loading = true;
      const data = {
        authCode: this.loginForm.code,
        telephone: this.loginForm.phone,
        username: this.loginForm.phone,
        password: this.loginForm.password,
      };
      registerApi(data).then((res) => {
        if (res.code == 200) {
          this.$message.success('注册成功！快去登录吧~');
          setTimeout(() => {
            this.$router.push({
              path: '/login',
              query: {
                redirect: location.href,
              },
            });
          }, 600);
        }
        this.loading = false;
      });
    },
  },
};
</script>

<style scoped>
@import url(./login.css);
::v-deep .el-form-item__label:before {
  color: rgba(255, 122, 0, 1) !important; /* 修改星号的颜色 */
}
.page_comStyle_login_box {
  position: relative;
  padding: 0px;
  /* overflow: hidden; */
}
.formBk {
  width: 470px;
  height: 489.347px;
  position: absolute;
  top: 35.137px;
  right: -113.1px;
}
.reginForm_box /deep/ .el-input {
  width: 276px !important;
}
.dark_container {
  background: #fdf3e7;
}

.agree {
  cursor: pointer;
  color: #ffb74a;
}
.accountText {
  color: #969696;
  font-size: 16px;
  font-family: 'PingFang SC';
  font-weight: 400;
  letter-spacing: 0.64px;
}
.reginForm_box {
  width: 487px;
}
.forget-pwd-input /deep/ .el-input__inner {
  width: 322px;
  border-radius: 50px;
  height: 52px;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.56px;
  padding: 0px 20px;
  color: #1b1b1b;
  border-color: rgba(150, 150, 150, 0.4) !important;
}
.forget-pwd-input /deep/.el-form-item__error {
  margin-left: 50px;
}
.show-pwdR {
  right: 43px;
  top: 4px;
  /* margin-top: 8px; */
}
.show-pwdR .svg-icon {
  width: 21.913px;
  height: 10.957px;
}
.show-pwdR .password1 {
  width: 21px;
  height: 21px;
  margin-top: 12px;
}
.show-pwdR .password2 {
  width: 21px;
  height: 21px;
  margin-top: 12px;
}
.loginFormStyle /deep/ .el-form-item__label {
  color: #1b1b1b;
  letter-spacing: 0.64px;
  font-size: 16px;
  /* margin-top: 7px; */
  font-family: 'PingFang SC';
  font-weight: 400;
  margin-right: 20px;
}
.loginFormStyle /deep/.reginForm_box .el-form-item {
  margin-bottom: 30px !important;
}
.loginBtnPwd {
  /* width: 194.5px; */
  height: 50px;
  border-radius: 60px;
  /* padding: 18px 0; */
  font-size: 16px;
  font-family: 'PingFang SC';
  letter-spacing: 0.64px;
  font-weight: 500;
}
.posionRight {
  background-color: transparent;
}
.code_wrap {
  background-color: transparent;
  /* line-height: 40px !important; */
}
.regin_head {
  border-bottom: none;
}
.forgetUnderscore {
  width: 100%;
  background: #fff;
}
.forgetUnderscoreContent {
  width: 100%;
  height: 0.5px;
  background: #ff7a00;
  margin: 0 auto;
}
.reginBk {
  background: linear-gradient(
    180deg,
    #fff1e2 16.51%,
    #fff9f3 44.06%,
    #fff9f3 74.95%,
    #ffe1c3 100%
  );
  padding-bottom: 80px;
}
</style>
